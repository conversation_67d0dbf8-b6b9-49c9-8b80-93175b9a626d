import PocketBase from 'pocketbase';
import { getImage } from 'astro:assets';

// Environment configuration
let POCKETBASE_URL = import.meta.env.POCKETBASE_URL;
const POCKETBASE_ADMIN_EMAIL = import.meta.env.POCKETBASE_ADMIN_EMAIL;
const POCKETBASE_ADMIN_PASSWORD = import.meta.env.POCKETBASE_ADMIN_PASSWORD;

// Clean up the PocketBase URL - remove admin panel paths
if (POCKETBASE_URL) {
    POCKETBASE_URL = POCKETBASE_URL.replace(/\/_\/.*$/, '').replace(/\/admin.*$/, '');
    if (!POCKETBASE_URL.endsWith('/')) {
        POCKETBASE_URL += '/';
    }
}

// Validate required environment variables
if (!POCKETBASE_URL || !POCKETBASE_ADMIN_EMAIL || !POCKETBASE_ADMIN_PASSWORD) {
    console.error('❌ Missing required PocketBase environment variables');
}

// Initialize PocketBase client
const pb = new PocketBase(POCKETBASE_URL);
pb.autoCancellation(false);

// Global state management
let allCollectionsCache = null;
let allCollectionDataCache = null;
let authenticationPromise = null;

/**
 * Authenticate with PocketBase using admin credentials
 */
async function authenticateAdmin() {
    if (authenticationPromise) {
        return authenticationPromise;
    }

    authenticationPromise = (async () => {
        try {
            if (!pb.authStore.isValid) {
                // Try different authentication methods in order of preference
                const authMethods = [
                    () => pb.collection('_superusers').authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD),
                    () => pb.admins.authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD),
                    () => pb.collection('users').authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD)
                ];

                let authData = null;
                let lastError = null;

                for (const authMethod of authMethods) {
                    try {
                        authData = await authMethod();
                        break;
                    } catch (error) {
                        lastError = error;
                        continue;
                    }
                }

                if (!authData) {
                    throw new Error(`Authentication failed: ${lastError?.message || 'Unknown error'}`);
                }
            }
            return true;
        } catch (error) {
            authenticationPromise = null;
            throw new Error(`Failed to authenticate with PocketBase: ${error.message}`);
        }
    })();

    return authenticationPromise;
}

/**
 * Discover all collections from PocketBase
 */
async function discoverAllCollections() {
    if (allCollectionsCache) {
        return allCollectionsCache;
    }

    await authenticateAdmin();

    try {
        const collections = await pb.collections.getFullList();
        allCollectionsCache = collections;
        return allCollectionsCache;
    } catch (error) {
        throw new Error(`Failed to discover collections: ${error.message}`);
    }
}


/**
 * Get collection ID by name
 */
function getCollectionIdByName(collections, name) {
    const collection = collections.find(c => c.name?.toLowerCase() === name.toLowerCase());
    return collection?.id || null;
}

// ===== FILE ATTACHMENT HANDLING =====

/**
 * Check if a value looks like a PocketBase filename
 */
function isFilename(value) {
    if (typeof value !== 'string' || !value.trim()) return false;
    const fileExtensions = /\.(jpg|jpeg|png|gif|webp|svg|pdf|doc|docx|txt|mp4|mp3|wav|zip|rar)$/i;
    return fileExtensions.test(value.trim());
}

/**
 * Check if a field contains file attachment data
 */
function isFileAttachmentField(value) {
    if (typeof value === 'string') {
        return isFilename(value);
    }

    if (Array.isArray(value) && value.length > 0) {
        const nonEmptyItems = value.filter(item => item && typeof item === 'string' && item.trim());
        if (nonEmptyItems.length === 0) return false;
        return nonEmptyItems.every(item => isFilename(item));
    }

    // Already transformed
    if (Array.isArray(value) && value.length > 0 && value[0] && typeof value[0] === 'object' && value[0].url) {
        return false;
    }

    return false;
}

/**
 * Transform PocketBase filenames to URL objects
 */
function transformFilenames(filenames, recordId, collectionId) {
    if (!Array.isArray(filenames)) return [];

    return filenames.map(filename => {
        if (typeof filename === 'string' && filename.trim()) {
            const url = `${POCKETBASE_URL}api/files/${collectionId}/${recordId}/${filename}`;
            return { url };
        }
        return null;
    }).filter(Boolean);
}

/**
 * Transform file field to URL objects
 */
function transformFileField(field, recordId, collectionId) {
    if (!field) return field;

    if (typeof field === 'string' && field.trim()) {
        const url = `${POCKETBASE_URL}api/files/${collectionId}/${recordId}/${field}`;
        return [{ url }];
    }

    if (Array.isArray(field)) {
        return transformFilenames(field, recordId, collectionId);
    }

    return field;
}

/**
 * Transform record file attachments dynamically
 */
function transformRecordFileAttachments(record) {
    if (!record || typeof record !== 'object') return record;

    const { id: recordId, collectionId } = record;
    if (!recordId || !collectionId) return record;

    const transformedRecord = { ...record };

    Object.keys(transformedRecord).forEach(fieldName => {
        const fieldValue = transformedRecord[fieldName];

        // Skip system fields and null/undefined values
        if (!fieldValue || ['id', 'collectionId', 'collectionName', 'created', 'updated'].includes(fieldName)) {
            return;
        }

        if (isFileAttachmentField(fieldValue)) {
            transformedRecord[fieldName] = transformFileField(fieldValue, recordId, collectionId);
        }
    });

    return transformedRecord;
}

// ===== CORE FUNCTIONALITY =====

/**
 * Merge events function (compatible with Airtable connector)
 */
function mergeEvents(record) {
    const otherEvents = record.get('other_events') || [];
    const fixedEvents = record.get('fixed_events') || [];
    const recordId = record.get('id');
    const collectionId = record.get('collectionId');

    const transformedOtherEvents = transformFilenames(otherEvents, recordId, collectionId);
    const transformedFixedEvents = transformFilenames(fixedEvents, recordId, collectionId);

    return [...transformedOtherEvents, ...transformedFixedEvents];
}

/**
 * Fetch data from a specific collection with comprehensive pagination
 */
async function fetchDataFromCollection(collectionName) {
    const collectionNameSanitized = collectionName.toLowerCase();

    try {
        await authenticateAdmin();
        const collections = await discoverAllCollections();
        const collectionId = getCollectionIdByName(collections, collectionName);

        if (!collectionId) {
            console.warn(`Collection "${collectionName}" not found`);
            return { table: collectionNameSanitized, data: [] };
        }

        // Fetch ALL records using pagination
        let allRecords = [];
        let page = 1;
        const perPage = 100;
        let hasMoreRecords = true;

        while (hasMoreRecords) {
            try {
                const records = await pb.collection(collectionName).getList(page, perPage);

                if (records && records.items && records.items.length > 0) {
                    allRecords = allRecords.concat(records.items);
                    page++;
                    hasMoreRecords = records.items.length === perPage && page <= records.totalPages;
                } else {
                    hasMoreRecords = false;
                }
            } catch (error) {
                console.error(`Error fetching page ${page} from ${collectionName}:`, error.message);
                hasMoreRecords = false;
            }
        }

        return { table: collectionNameSanitized, data: allRecords };
    } catch (error) {
        console.error(`Error fetching data from ${collectionName}:`, error.message);
        return { table: collectionNameSanitized, data: [] };
    }
}

/**
 * Fetch all collections from PocketBase
 */
async function fetchAllCollections() {
    if (allCollectionDataCache) {
        return allCollectionDataCache;
    }

    await authenticateAdmin();
    const collections = await discoverAllCollections();

    // Fetch data from ALL collections
    const allCollectionData = await Promise.all(
        collections.map(async (collection) => {
            try {
                const data = await fetchDataFromCollection(collection.name);
                return data;
            } catch (error) {
                console.warn(`Failed to fetch ${collection.name}:`, error.message);
                return { table: collection.name.toLowerCase(), data: [] };
            }
        })
    );

    allCollectionDataCache = allCollectionData;
    return allCollectionDataCache;
}

/**
 * Legacy function for compatibility - now fetches all collections
 */
async function fetchTablesFromPocketBase() {
    return fetchAllCollections();
}

/**
 * Normalize record function (compatible with other connectors)
 */
const normalizeRecord = (record) => {
    const recordWithGet = { get: (field) => record[field] };
    const mergedEvents = mergeEvents(recordWithGet);

    return {
        id: record.id,
        ...record,
        other_events: mergedEvents
    };
};

// ===== INITIALIZATION AND EXPORTS =====



/**
 * Get table data by collection name - provides direct access to any collection
 */
let getTable;

let initPromise;

if (typeof window === 'undefined') {
    // Server-side initialization
    initPromise = (async () => {
        try {
            const tables = await fetchAllCollections();

            // Create the getTable function that can access any collection
            getTable = (tableName) => {
                const table = tables.find((t) => t.table === tableName.toLowerCase());
                return table ? table.data : [];
            };

            return { initialized: true };
        } catch (error) {
            console.error('Error initializing PocketBase connector:', error.message);
            // Create a fallback getTable function that returns empty arrays
            getTable = () => [];
            return { initialized: false };
        }
    })();
} else {
    // Client-side - return empty data
    getTable = () => [];
    initPromise = Promise.resolve({ initialized: false });
}

// Initialize the connector
await initPromise;

// Helper function to process services with image optimization
async function getProcessedServices() {
    const services = getTable('services').map(normalizeRecord);
    const servicesRaw = services.filter(s => s.published);

    return Promise.all(servicesRaw.map(async (s) => {
        try {
            if (s.cover) {
                let coverFilename = Array.isArray(s.cover) ? s.cover[0] : s.cover;

                if (coverFilename && typeof coverFilename === 'string' && coverFilename.trim()) {
                    let coverUrl;
                    try {
                        coverUrl = pb.files.getURL(s, coverFilename);
                    } catch (urlError) {
                        if (POCKETBASE_URL && s.collectionId && s.id) {
                            coverUrl = `${POCKETBASE_URL}api/files/${s.collectionId}/${s.id}/${coverFilename}`;
                        } else {
                            return s;
                        }
                    }

                    if (coverUrl) {
                        return {
                            ...s,
                            cover: await getImage({ src: coverUrl, format: 'webp', width: 1080, height: 1080 })
                        };
                    }
                }
            }
            return s;
        } catch (error) {
            console.warn('Error processing service image:', error.message);
            return s;
        }
    }));
}

// Export collections dynamically - can access any collection by name
export const latinEvents = getTable('dancing_agenda_view').map(record => transformRecordFileAttachments(normalizeRecord(record)));
export const socialEvents = getTable('social_agenda_view').map(record => transformRecordFileAttachments(normalizeRecord(record)));
export const reviews = getTable('reviews').map(record => transformRecordFileAttachments(normalizeRecord(record)));
export const vocabulary = getTable('vocabulary').map(normalizeRecord);
export const bio = getTable('bio').map(record => transformRecordFileAttachments(normalizeRecord(record)));
export const settings = getTable('settings').map(normalizeRecord);
export const properties = getTable('housing_view').map(record => transformRecordFileAttachments(normalizeRecord(record)));
export const services = await getProcessedServices();

// Export utility functions for direct collection access
export { getTable, fetchTablesFromPocketBase, fetchAllCollections };
